//
//  RecommendModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/6/22.
//

import Foundation
import SmartCodable
import SwiftDate

/// 帖子类型枚举
enum RecommendType: Int, Codable {
    case unknown = 0
    case official = 1  // 官方/广告
    case findPeople = 2  // 找人
    case photography = 3  // 摄影摄像
    case findHouse = 4  // 找房
    case findIdle = 5  // 找闲置
    case findCar = 6  // 找车
    case talk = 7  // 有话说
    case askAny = 8  // 随便问
    
    var displayName: String {
        switch self {
        case .official: return "官方/广告"
        case .findPeople: return "找人"
        case .photography: return "摄影摄像"
        case .findHouse: return "找房"
        case .findIdle: return "找闲置"
        case .findCar: return "找车"
        case .talk: return "有话说"
        case .askAny: return "随便问"
        case .unknown: return "未知"
        }
    }
    var displayTextColor: UIColor {
        switch self {
        case .official: return UIColor(hexString: "05C3A3", transparency: 1)! //"官方/广告"
        case .findPeople: return UIColor(hexString: "05C3A3", transparency: 1)! //"找人"
        case .photography: return UIColor(hexString: "05C3A3", transparency: 1)! //"摄影摄像"
        case .findHouse: return UIColor(hexString: "DA7527", transparency: 1)! //"找房"
        case .findIdle: return UIColor(hexString: "05C3A3", transparency: 1)! //"找闲置"
        case .findCar: return UIColor(hexString: "2AB0FF", transparency: 1)! //"找车"
        case .talk: return UIColor(hexString: "FF3C1E", transparency: 1)! //"有话说"
        case .askAny: return UIColor(hexString: "2B2C2F", transparency: 0.8)! //"随便问"
        case .unknown: return UIColor(hexString: "05C3A3", transparency: 1)! //"未知"
        }
    }
    var displayBackColor: UIColor {
        switch self {
        case .official: return UIColor(hexString: "00FFD2", transparency: 0.08)! //"官方/广告"
        case .findPeople: return UIColor(hexString: "00FFD2", transparency: 0.08)! //"找人"
        case .photography: return UIColor(hexString: "00FFD2", transparency: 0.08)! //"摄影摄像"
        case .findHouse: return UIColor(hexString: "E38E23", transparency: 0.08)! //"找房"
        case .findIdle: return UIColor(hexString: "00FFD2", transparency: 0.08)! //"找闲置"
        case .findCar: return UIColor(hexString: "2AB0FF", transparency: 0.08)! //"找车"
        case .talk: return UIColor(hexString: "FF0F00", transparency: 0.08)! //"有话说"
        case .askAny: return UIColor(hexString: "2B2C2F", transparency: 0.08)! //"随便问"
        case .unknown: return UIColor(hexString: "00FFD2", transparency: 0.08)! //"未知"
        }
    }
}

struct RecommendUserModel: SmartCodable {
    var id: String = ""
    var nickname: String = ""
    var avatar: String = ""
}
/// 推荐帖子模型
struct RecommendModel: SmartCodable {
    var id: String = ""
    var userId: String = ""
    var title: String = ""
    var img_size: String = ""
    var nickname: String = ""
    var user: RecommendUserModel?
    var description: String = ""
    var img_urls: [String] = [] 
    var img: String = ""
    var price: String = ""
    var originPrice: String?
    var location: String = ""
    var contactInfo: String = ""
    var status: String = ""
    var created_at: String = ""
    var visibility: String = ""
    var isDeleted: String = ""
    var views: String = ""
    var quantity: String = ""
    var postageType: String = ""
    var type: Int = 0
    var is_liked: Bool = false
    var is_favorited: Bool = false
    var like_count: Int = 0
    var comment_count: Int = 0
    var favorite_count: Int = 0

    
    var cellHeight = 0.0
    // 计算属性，方便使用枚举
    var recommendType: RecommendType {
        return RecommendType(rawValue: type) ?? .unknown
    }   
       
    var imageSize:Double{
        let minValue = 9.0 / 16.0 // ≈0.5625
        let maxValue = 16.0 / 9.0 // ≈1.7778
        guard let size = convertImageSize(self.img_size) else {
            return 1
        }
        let aspectRatio = Double(size.width) / Double(size.height)
        if aspectRatio < minValue {
               return minValue
           } else if aspectRatio > maxValue {
               return maxValue
           } else {
               return aspectRatio
           }
    }
  
    func convertImageSize(_ sizeString: String) -> (width: Int, height: Int)? {
        // 1. 分割字符串
        let components = sizeString.components(separatedBy: "/")
        
        // 2. 验证分割结果
        guard components.count == 2,
              let widthStr = components.first,
              let heightStr = components.last,
              let width = Int(widthStr),   // 字符串转整数
              let height = Int(heightStr), // 字符串转整数
              height != 0                  // 避免除零错误
        else {
            return nil
        }
        
        return (width, height)
    }

}

