//
//  CombinedPaymentController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/24.
//

import UIKit
import Combine

class CombinedPaymentController: BasePresentController {

    // MARK: - Properties



    /// 订单总金额
    var totalAmount: Float = 0.0

    /// 可用余额
    var availableBalance: Float = 0.0

    // MARK: - 订单相关信息

    /// 服务订单模型
    private var serviceModel: HomeDetailModel?

    /// 商品订单模型
    private var productModel: ProductDetailModel?

    /// 地址模型
    private var addressModel: ProductAddressModel?

    /// 优惠券模型
    private var couponModel: CouponModel?

    /// 支付ViewModel
    private var paymentViewModel: CombinedPaymentViewModel?

    /// 订单创建回调 - 传递支付数据给外部处理下单
    let orderCreationPublisher = PassthroughSubject<PaymentData, Never>()

    /// 订单创建结果回调 - 接收外部下单结果
    let orderCreationResultSubject = PassthroughSubject<(Bool, Int, String), Never>()

    // MARK: - UI Components

    /// 支付组合视图
    private lazy var paymentView = CombinedPaymentView().then {
        $0.delegate = self
    }

    // MARK: - Lifecycle


    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        setupPaymentView()
        initializePaymentViewModel()
        fetchWalletBalance()
    }

    // MARK: - UI Setup

    override func configUI() {
        configView(title: "选择支付方式", bottomTitle: "立即支付")
        self.bottomButton.gradientColors = [UIColor(hexString: "FF5D48")!,UIColor(hexString: "FF3434")!]
        self.bottomButton.gradientDirection = .leftToRight

        // 添加支付视图
        contentView.addSubview(paymentView)
        paymentView.snp.makeConstraints { make in
            make.top.left.bottom.right.equalToSuperview()
        }
    }

    // MARK: - Private Methods

    /// 设置支付视图
    private func setupPaymentView() {
        paymentView.configure(
            totalAmount: totalAmount,
            availableBalance: availableBalance
        )
    }

    /// 初始化支付ViewModel
    private func initializePaymentViewModel() {
        paymentViewModel = CombinedPaymentViewModel()

        // 监听支付流程状态变化
        paymentViewModel?.$paymentFlowState
            .receive(on: DispatchQueue.main)
            .sink { [weak self] state in
                self?.handlePaymentFlowStateChange(state)
            }
            .store(in: &cancellables)

        // 监听可用余额变化
        paymentViewModel?.$availableBalance
            .receive(on: DispatchQueue.main)
            .sink { [weak self] balance in
                self?.handleAvailableBalanceUpdate(balance)
            }
            .store(in: &cancellables)

        // 监听订单创建结果
        orderCreationResultSubject
            .receive(on: DispatchQueue.main)
            .sink { [weak self] success, orderId, message in
                self?.handleOrderCreationResult(success: success, orderId: orderId, message: message)
            }
            .store(in: &cancellables)
    }

    /// 获取钱包余额
    private func fetchWalletBalance() {
        paymentViewModel?.fetchWalletBalance()
    }

    /// 处理可用余额更新
    /// - Parameter balance: 可用余额
    private func handleAvailableBalanceUpdate(_ balance: Float) {
        // 更新本地余额
        self.availableBalance = balance

        // 更新支付视图的余额显示
        paymentView.updateAvailableBalance(balance)

        // 更新底部按钮标题
        updateBottomButtonTitle()
    }

    /// 更新底部按钮标题
    private func updateBottomButtonTitle() {
        let buttonTitle = "立即支付 ¥\(String(format: "%.2f", totalAmount))"
        bottomButton.setTitle(buttonTitle, for: .normal)
    }

    // MARK: - Bindings

    override func setupBindings() {
        super.setupBindings()

        // 确认支付按钮
        bottomButton.tapPublisher
            .sink { [weak self] _ in
                self?.confirmPayment()
            }
            .store(in: &cancellables)
    }

    private func confirmPayment() {
        let paymentData = paymentView.getPaymentData()

        let totalPaymentAmount = paymentData.walletAmount + paymentData.thirdPartyAmount
        guard abs(totalPaymentAmount - totalAmount) < 0.01 else {
            showErrorAlert("支付金额不正确，请重新输入")
            return
        }

        guard paymentData.walletAmount <= availableBalance else {
            showErrorAlert("余额支付金额不能超过可用余额")
            return
        }

        if paymentData.thirdPartyAmount > 0 {
            if !isThirdPartyPaymentAvailable(paymentData.paymentType) {
                showErrorAlert("当前支付方式不可用，请选择其他支付方式")
                return
            }
        }

        startPaymentProcess(with: paymentData)
    }

    private func isThirdPartyPaymentAvailable(_ paymentType: String) -> Bool {
        switch paymentType {
        case "alipay":
            return true
        case "wechat":
            return true
        default:
            return false
        }
    }

    /// 开始支付流程 - 发送订单创建请求给外部处理
    private func startPaymentProcess(with paymentData: PaymentData) {
        guard let paymentViewModel = paymentViewModel else {
            showErrorAlert("系统错误，请重试")
            return
        }

        // 确定支付方式
        let paymentMethod: String
        if paymentData.walletAmount > 0 && paymentData.thirdPartyAmount > 0 {
            paymentMethod = PaymentMethodIdentifiers.mix
        } else if paymentData.walletAmount > 0 {
            paymentMethod = PaymentMethodIdentifiers.wallet
        } else {
            paymentMethod = paymentData.paymentType == "alipay" ? PaymentMethodIdentifiers.alipay : PaymentMethodIdentifiers.wechat
        }

        // 设置支付参数到ViewModel
        paymentViewModel.setPaymentParameters(
            walletAmount: paymentData.walletAmount,
            thirdPartyAmount: paymentData.thirdPartyAmount,
            paymentMethod: paymentMethod,
            thirdPartyMethod: paymentData.paymentType
        )

        // 显示加载状态
        showLoadingIndicator(text: "创建订单中...")

        // 通过Combine发送订单创建请求给外部处理
        orderCreationPublisher.send(paymentData)
    }

    /// 处理订单创建结果
    private func handleOrderCreationResult(success: Bool, orderId: Int, message: String) {
        if success {
            // 订单创建成功，开始支付流程
            startPaymentWithOrderId(orderId)
        } else {
            // 订单创建失败，显示错误信息
            hideLoadingIndicator()
            showErrorAlert("创建订单失败：\(message)")
        }
    }

    private func startPaymentWithOrderId(_ orderId: Int) {
        guard let paymentViewModel = paymentViewModel else {
            hideLoadingIndicator()
            showErrorAlert("系统错误，请重试")
            return
        }

        showLoadingIndicator(text: "发起支付...")

        paymentViewModel.initiatePayment(orderId: orderId) { [weak self] success, message in
            DispatchQueue.main.async {
                if !success {
                    self?.hideLoadingIndicator()
                    self?.showErrorAlert("支付失败：\(message)")
                }
            }
        }
    }

    /// 显示错误提示
    override func showErrorAlert(_ message: String) {
        let alert = UIAlertController(title: "提示", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    // MARK: - Public Methods

    /// 设置订单总金额
    func setTotalAmount(_ amount: Float) {
        totalAmount = amount
        paymentView.updateTotalAmount(amount)
    }

    /// 设置订单信息
    /// - Parameters:
    ///   - serviceModel: 服务订单模型
    ///   - productModel: 商品订单模型
    ///   - addressModel: 地址模型
    ///   - couponModel: 优惠券模型
    func setOrderInfo(serviceModel: HomeDetailModel?, productModel: ProductDetailModel?, addressModel: ProductAddressModel?, couponModel: CouponModel?) {
        self.serviceModel = serviceModel
        self.productModel = productModel
        self.addressModel = addressModel
        self.couponModel = couponModel
    }

    // MARK: - 支付流程状态处理

    private func handlePaymentFlowStateChange(_ state: PaymentFlowState) {
        switch state {
        case .idle:
            hideLoadingIndicator()

        case .creatingOrder:
            showLoadingIndicator(text: "创建订单中...")

        case .orderCreated(let orderId):
            print("订单创建成功，订单ID: \(orderId)")

        case .initiatingPayment:
            showLoadingIndicator(text: "获取支付参数中...")

        case .waitingThirdPartyPayment:
            showLoadingIndicator(text: "等待支付...")

        case .notifyingPaymentSuccess:
            showLoadingIndicator(text: "处理支付结果中...")

        case .paymentCompleted(let orderId):
            hideLoadingIndicator()
            handlePaymentSuccess(orderId: orderId)

        case .paymentFailed(let error):
            hideLoadingIndicator()
            handlePaymentFailed(error: error)
        }
    }

    private func handlePaymentSuccess(orderId: Int) {
        dismiss(animated: true) { [weak self] in
            self?.showPaymentSuccessAlert(orderId: orderId)
        }
    }

    private func handlePaymentFailed(error: String) {
        showErrorAlert("支付失败：\(error)")
    }

    private func showPaymentSuccessAlert(orderId: Int) {
        guard let currentVC = UIViewController.getCurrentViewController() else { return }

        let alert = UIAlertController(title: "支付成功", message: "订单支付成功，即将跳转到订单详情", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default) { _ in
            self.navigateToOrderDetail(orderId: orderId)
        })
        currentVC.present(alert, animated: true)
    }

    private func navigateToOrderDetail(orderId: Int) {
        guard let currentVC = UIViewController.getCurrentViewController(),
              let navigationController = currentVC.navigationController else { return }

        navigationController.popToRootViewController(animated: true)

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            let orderDetailController = ProductOrderDetailController()
            orderDetailController.orderId = orderId
            navigationController.pushViewController(orderDetailController, animated: true)
        }
    }

    /// 显示加载指示器
    /// - Parameter text: 加载文本
    private func showLoadingIndicator(text: String = "处理中...") {
        MBProgressHUD.showLoading(in: self.view, text: text)
    }

    /// 隐藏加载指示器
    private func hideLoadingIndicator() {
        MBProgressHUD.hide(for: self.view, animated: true)
    }
}

// MARK: - CombinedPaymentViewDelegate

extension CombinedPaymentController: CombinedPaymentViewDelegate {

    func paymentView(_ view: CombinedPaymentView, didUpdatePaymentAmount amount: Float) {
        let paymentData = view.getPaymentData()
        let totalPaymentAmount = paymentData.walletAmount + paymentData.thirdPartyAmount
        let isValidAmount = abs(totalPaymentAmount - totalAmount) < 0.01 && paymentData.walletAmount <= availableBalance
        print("支付金额变化 - 余额: ¥\(paymentData.walletAmount), 第三方: ¥\(paymentData.thirdPartyAmount), 总计: ¥\(totalPaymentAmount)")
    }
}
