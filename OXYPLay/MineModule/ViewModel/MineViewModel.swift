//
//  MineViewModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/26.
//

import Foundation
import Combine

/// 我的页面类型枚举
enum MinePageType: Int, CaseIterable {
    case myPosts = 0        // 我发布的
    case favorites = 1      // 我的收藏
    case likes = 2          // 我的点赞
    case offline = 3        // 已下架
    case history = 4        // 浏览历史
    case draft = 5        // 浏览历史

    var title: String {
        switch self {
        case .myPosts: return "我发布的"
        case .favorites: return "我的收藏"
        case .likes: return "我的点赞"
        case .offline: return "已下架"
        case .history: return "浏览历史"
        case .draft: return "草稿"
        }
    }
}

/// 我的页面ViewModel
class MineViewModel: BaseViewModel {

    // MARK: - Published Properties

    /// 推荐列表数据
    @Published var recommendList: [RecommendModel] = []

    /// 草稿数据（仅在我发布的页面有效）
    @Published var draftModel: MyPostModel?

    /// 当前页面类型
    @Published var pageType: MinePageType = .myPosts

    /// 删除草稿结果
    let deleteDraftResultPublisher = PassthroughSubject<Bool, Never>()

    /// 点赞操作结果
    let likeResultPublisher = PassthroughSubject<Bool, Never>()

    // MARK: - Private Properties


    // MARK: - 初始化

    init(pageType: MinePageType) {
        self.pageType = pageType
        super.init()
    }

    // MARK: - 数据加载方法

    /// 获取数据
    /// - Parameter refresh: 是否为刷新操作
    func fetchData(refresh: Bool = false) {
        switch pageType {
        case .myPosts,.draft:
            fetchMyPosts(refresh: refresh)
        case .favorites:
            fetchFavoritePosts(refresh: refresh)
        case .likes:
            fetchLikedPosts(refresh: refresh)
        case .offline:
            fetchOfflinePosts(refresh: refresh)
        case .history:
            fetchBrowseHistory(refresh: refresh)
        }
    }

    /// 获取我发布的帖子
    private func fetchMyPosts(refresh: Bool) {
        if refresh {
            refreshState = .headerRefreshing
            currentPage = 1
            hasMoreData = true
        } else {
            guard hasMoreData else {
                refreshState = .noMoreData
                return
            }
            refreshState = .footerLoading
            currentPage += 1
        }

     
            let request = MyPostsRequest(page: currentPage, limit: pageSize)

            requestModel(MinePostService.myPosts(params: request), type: MyPostsResponseModel.self)
                .receive(on: DispatchQueue.main)
                .sink(
                    receiveCompletion: { [weak self] completion in
                        if case let .failure(error) = completion {
                            self?.handleError(error, operation: "获取我的帖子")
                        }
                    },
                    receiveValue: { [weak self] response in
                        self?.handleMyPostsResponse(response, isRefresh: refresh)
                    }
                )
                .store(in: &cancellables)
    }

    /// 获取收藏的帖子
    private func fetchFavoritePosts(refresh: Bool) {
        if refresh {
            refreshState = .headerRefreshing
            currentPage = 1
            hasMoreData = true
        } else {
            guard hasMoreData else {
                refreshState = .noMoreData
                return
            }
            refreshState = .footerLoading
            currentPage += 1
        }

      
            let request = FavoritePostsRequest(page: currentPage, limit: pageSize)

            requestModel(MinePostService.favoritePosts(params: request), type: FavoriteResponseModel.self)
                .receive(on: DispatchQueue.main)
                .sink(
                    receiveCompletion: { [weak self] completion in
                        if case let .failure(error) = completion {
                            self?.handleError(error)
                        }
                    },
                    receiveValue: { [weak self] response in
                        self?.handleFavoriteResponse(response, isRefresh: refresh)
                    }
                )
                .store(in: &cancellables)
    }

    /// 获取点赞的帖子
    private func fetchLikedPosts(refresh: Bool) {
        if refresh {
            refreshState = .headerRefreshing
            currentPage = 1
            hasMoreData = true
        } else {
            guard hasMoreData else {
                refreshState = .noMoreData
                return
            }
            refreshState = .footerLoading
            currentPage += 1
        }

    
            let request = LikedPostsRequest(page: currentPage, limit: pageSize)

            requestModel(MinePostService.likedPosts(params: request), type: FavoriteResponseModel.self)
                .receive(on: DispatchQueue.main)
                .sink(
                    receiveCompletion: { [weak self] completion in
                        if case let .failure(error) = completion {
                            self?.handleError(error)
                        }
                    },
                    receiveValue: { [weak self] response in
                        self?.handleLikedResponse(response, isRefresh: refresh)
                    }
                )
                .store(in: &cancellables)
    }

    /// 获取已下架的帖子
    private func fetchOfflinePosts(refresh: Bool) {
        if refresh {
            refreshState = .headerRefreshing
            currentPage = 1
            hasMoreData = true
        } else {
            guard hasMoreData else {
                refreshState = .noMoreData
                return
            }
            refreshState = .footerLoading
            currentPage += 1
        }

       
            let request = OfflinePostsRequest(page: currentPage, limit: pageSize)

            requestModel(MinePostService.offlinePosts(params: request), type: OfflinePostsResponseModel.self)
                .receive(on: DispatchQueue.main)
                .sink(
                    receiveCompletion: { [weak self] completion in
                        if case let .failure(error) = completion {
                            self?.handleError(error)
                        }
                    },
                    receiveValue: { [weak self] response in
                        self?.handleOfflineResponse(response, isRefresh: refresh)
                    }
                )
                .store(in: &cancellables)
    }

    /// 获取浏览历史
    private func fetchBrowseHistory(refresh: Bool) {
        if refresh {
            refreshState = .headerRefreshing
            currentPage = 1
            hasMoreData = true
        } else {
            guard hasMoreData else {
                refreshState = .noMoreData
                return
            }
            refreshState = .footerLoading
            currentPage += 1
        }

  
            let request = BrowseHistoryRequest(page: currentPage, limit: pageSize)

            requestModel(MinePostService.browseHistory(params: request), type: BrowseHistoryResponseModel.self)
                .receive(on: DispatchQueue.main)
                .sink(
                    receiveCompletion: { [weak self] completion in
                        if case let .failure(error) = completion {
                            self?.handleError(error, operation: "获取浏览历史")
                        }
                    },
                    receiveValue: { [weak self] response in
                        self?.handleHistoryResponse(response, isRefresh: refresh)
                    }
                )
                .store(in: &cancellables)
    }

    /// 删除草稿
    /// - Parameter draftId: 草稿ID
    func deleteDraft(draftId: String) {
        let request = DeleteDraftRequest(draftId: draftId)

        requestModel(MinePostService.deleteDraft(params: request), type: EmptyResponse.self)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case let .failure(error) = completion {
                        self?.handleError(error)
                        self?.deleteDraftResultPublisher.send(false)
                    }
                },
                receiveValue: { [weak self] _ in
                    // 删除成功，清除草稿数据并刷新列表
                    self?.draftModel = nil
                    self?.deleteDraftResultPublisher.send(true)
                    self?.fetchData(refresh: true)
                }
            )
            .store(in: &cancellables)
    }

    // MARK: - 帖子互动操作

    /// 帖子点赞/取消点赞
    /// - Parameters:
    ///   - type: 帖子类型
    ///   - postId: 帖子ID
    func toggleLike(type: Int, postId: String) {
        guard !postId.isEmpty else {
            print("帖子ID不能为空")
            likeResultPublisher.send(false)
            return
        }

        let params = RequestParameters([
            "post_id": postId,
            "type": type
        ])

        request(CommentService.likeToggle(params: params))
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case let .failure(error) = completion {
                        self?.handleError(error)
                        self?.likeResultPublisher.send(false)
                    }
                },
                receiveValue: { [weak self] _ in
                    // 点赞成功，刷新当前页面数据
                    self?.fetchData(refresh: true)
                    self?.likeResultPublisher.send(true)
                    print("帖子点赞操作成功")
                }
            )
            .store(in: &cancellables)
    }

    // MARK: - 重写BaseViewModel方法

    override func refreshData() {
        fetchData(refresh: true)
    }

    override func loadMoreData() {
        fetchData(refresh: false)
    }

    // MARK: - 数据处理方法

    /// 处理我发布的帖子响应
    private func handleMyPostsResponse(_ response: MyPostsResponseModel, isRefresh: Bool) {
        // 处理草稿数据
        if isRefresh {
            draftModel = response.draft
        }

        // 转换为RecommendModel
        let newModels = response.posts.map { $0.toRecommendModel() }

        if isRefresh {
            recommendList = newModels
        } else {
            recommendList.append(contentsOf: newModels)
        }

        // 更新分页状态
        hasMoreData = newModels.count >= pageSize
        refreshState = isRefresh ? .refreshSuccess : .loadMoreSuccess
    }

    /// 处理收藏响应
    private func handleFavoriteResponse(_ response: FavoriteResponseModel, isRefresh: Bool) {
        let newModels = response.list.map { $0.toRecommendModel() }

        if isRefresh {
            recommendList = newModels
        } else {
            recommendList.append(contentsOf: newModels)
        }

        // 更新分页状态
        hasMoreData = response.pagination.page < response.pagination.total_page
        refreshState = isRefresh ? .refreshSuccess : .loadMoreSuccess
    }

    /// 处理点赞响应
    private func handleLikedResponse(_ response: FavoriteResponseModel, isRefresh: Bool) {
        let newModels = response.list.map { $0.toRecommendModel() }

        if isRefresh {
            recommendList = newModels
        } else {
            recommendList.append(contentsOf: newModels)
        }

        // 更新分页状态
        hasMoreData = response.pagination.page < response.pagination.total_page
        refreshState = isRefresh ? .refreshSuccess : .loadMoreSuccess
    }

    /// 处理已下架响应
    private func handleOfflineResponse(_ response: OfflinePostsResponseModel, isRefresh: Bool) {
        let newModels = response.posts.map { $0.toRecommendModel() }

        if isRefresh {
            recommendList = newModels
        } else {
            recommendList.append(contentsOf: newModels)
        }

        // 更新分页状态
        hasMoreData = newModels.count >= pageSize
        refreshState = isRefresh ? .refreshSuccess : .loadMoreSuccess
    }

    /// 处理浏览历史响应
    private func handleHistoryResponse(_ response: BrowseHistoryResponseModel, isRefresh: Bool) {
        let newModels = response.list.map { $0.toRecommendModel() }

        if isRefresh {
            recommendList = newModels
        } else {
            recommendList.append(contentsOf: newModels)
        }

        // 更新分页状态
        hasMoreData = newModels.count >= pageSize
        refreshState = isRefresh ? .refreshSuccess : .loadMoreSuccess
    }

   
    // MARK: - 错误处理

    /// 处理网络错误
    override func handleError(_ error: NetworkError, operation: String = "请求") {
        // 调用父类方法处理空数据状态
        super.handleError(error, operation: operation)

        // 设置刷新状态
        refreshState = currentPage == 1 ? .refreshFailure("加载失败") : .loadMoreFailure("加载失败")

        // 添加特定的错误处理逻辑
        switch error {
        case .networkError(let response):
            print("网络错误: \(response.message)")
        case .decodingError(let message):
            print("数据解析错误: \(message)")
        case .noConnection:
            print("网络连接失败")
        case .tokenExpired:
            print("Token已过期")
        case .tokenError:
            print("Token错误")
        }
    }
}
