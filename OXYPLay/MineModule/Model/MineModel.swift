//
//  MineModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/26.
//

import Foundation
import SmartCodable

// MARK: - 我发布的帖子模型

/// 我发布的帖子响应模型
struct MyPostsResponseModel: SmartCodable {
    /// 草稿帖子（仅一条，如存在）
    var draft: MyPostModel?
    
    /// 正式发布的帖子列表
    var posts: [MyPostModel] = []
    
    /// 正式发布帖子的总数量（不含草稿）
    var total: Int = 0
    
    /// 当前页码
    var page: Int = 1
    
    /// 每页条数
    var limit: Int = 10
}

/// 我的帖子模型
struct MyPostModel: SmartCodable {
    /// 帖子ID
    var id: String = ""
    
    /// 发布人用户ID
    var user_id: String = ""
    
    /// 帖子标题
    var title: String = ""
    
    /// 帖子描述内容
    var description: String = ""
    
    /// 帖子图片列表（JSON字符串）
    var img_urls: String = ""
    
    /// 封面图URL，取自img_urls第一张图
    var cover_img: String = ""
    
    /// 售价
    var price: String?
    
    /// 原价
    var origin_price: String?
    
    /// 发布地点
    var location: String = ""
    
    /// 联系方式
    var contact_info: String = ""
    
    /// 状态编号
    var status: String = ""
    
    /// 状态说明文本
    var status_txt: String = ""
    
    /// 服务类型编号
    var type: Int = 0
    
    /// 类型名称
    var type_txt: String = ""
    
    /// 可见性设定
    var visibility: String = ""
    
    /// 是否删除
    var is_deleted: String = ""
    
    /// 浏览量
    var views: String = ""
    
    /// 剩余库存数量
    var quantity: String = ""
    
    /// 运费类型
    var postage_type: String?
    
    /// 固定运费
    var postage_fee: String?
    
    /// 创建时间
    var created_at: String = ""
    
    /// 更新时间
    var updated_at: String = ""
    
    /// 点赞数
    var like_count: Int = 0
    /// 评论数
    var comment_count: Int = 0
    var user: RecommendUserModel?
    var is_liked = false
    /// 是否为草稿
    var isDraft: Bool {
        return status == "4"
    }
    
    /// 转换为RecommendModel
    func toRecommendModel() -> RecommendModel {
        var model = RecommendModel()
        model.id = id
        model.userId = user_id
        model.title = title
        model.description = description
        model.img = cover_img
        model.price = price ?? ""
        model.location = location
        model.contactInfo = contact_info
        model.status = status
        model.created_at = created_at
        model.visibility = visibility
        model.isDeleted = is_deleted
        model.views = views
        model.quantity = quantity
        model.type = type
        model.like_count = like_count
        model.comment_count = comment_count
        model.user = user
        model.is_liked = is_liked

        // 解析图片URLs
        if let data = img_urls.data(using: .utf8),
           let imageArray = try? JSONSerialization.jsonObject(with: data) as? [[String: Any]] {
            model.img_urls = imageArray.compactMap { $0["url"] as? String }
        }
        
        return model
    }
}

// MARK: - 收藏和点赞模型

/// 收藏/点赞响应模型
struct FavoriteResponseModel: SmartCodable {
    /// 帖子列表
    var list: [FavoritePostModel] = []
    
    /// 分页信息
    var pagination: PaginationModel = PaginationModel()
}

/// 收藏/点赞的帖子模型
struct FavoritePostModel: SmartCodable {
    /// 帖子ID
    var id: Int = 0
    
    /// 帖子标题
    var title: String = ""
    
    /// 帖子描述
    var description: String = ""
    
    /// 封面图URL
    var cover_img: String = ""
    
    /// 点赞数量
    var like_count: Int = 0
    
    /// 当前用户是否点赞
    var is_liked: Bool = false
    
    /// 发布人昵称
    var nickname: String = ""
    
    /// 发布人头像URL
    var avatar: String = ""
    
    /// 价格
    var price: Float?
    
    /// 类型编号
    var type: Int = 0
    
    /// 类型名称
    var type_txt: String = ""
    
    /// 帖子状态文本
    var status_txt: String = ""
    
    /// 原始帖子ID
    var post_id: Int = 0
    var user: RecommendUserModel?
    /// 转换为RecommendModel
    func toRecommendModel() -> RecommendModel {
        var model = RecommendModel()
        model.id = String(id)
        model.title = title
        model.description = description
        model.img = cover_img
        model.price = price != nil ? String(format: "%.2f", price!) : ""
        model.type = type
        model.like_count = like_count
        model.is_liked = is_liked
        model.is_liked = is_liked
        // 创建用户信息
        var user = RecommendUserModel()
        user.nickname = nickname
        user.avatar = avatar
        model.user = user
        
        return model
    }
}

/// 分页信息模型
struct PaginationModel: SmartCodable {
    /// 当前页码
    var page: Int = 1
    
    /// 每页条数
    var limit: Int = 10
    
    /// 总记录数
    var total: Int = 0
    
    /// 总页数
    var total_page: Int = 0
}

// MARK: - 已下架帖子模型

/// 已下架帖子响应模型
struct OfflinePostsResponseModel: SmartCodable {
    /// 服务帖列表
    var posts: [OfflinePostModel] = []
    
    /// 总记录数
    var total: Int = 0
    
    /// 当前页码
    var page: Int = 1
    
    /// 每页条数
    var limit: Int = 10
}

/// 已下架帖子模型
struct OfflinePostModel: SmartCodable {
    /// 帖子ID
    var id: Int = 0
    
    /// 帖子标题
    var title: String = ""
    
    /// 帖子描述
    var description: String = ""
    
    /// 封面图URL
    var cover_img: String = ""
    
    /// 类型编号
    var type: Int = 0
    
    /// 类型文本
    var type_txt: String = ""
    
    /// 状态文本（固定为"已下架"）
    var status_txt: String = ""
    
    /// 点赞数量
    var like_count: Int = 0
    
    /// 评论数量
    var comment_count: Int = 0
    var user: RecommendUserModel?
    var is_liked = false
    
    /// 转换为RecommendModel
    func toRecommendModel() -> RecommendModel {
        var model = RecommendModel()
        model.id = String(id)
        model.title = title
        model.description = description
        model.img = cover_img
        model.type = type
        model.like_count = like_count
        model.comment_count = comment_count
        model.user = user
        model.is_liked = is_liked
        return model
    }
}

// MARK: - 浏览历史模型

/// 浏览历史响应模型
struct BrowseHistoryResponseModel: SmartCodable {
    /// 总记录数
    var total: Int = 0
    
    /// 当前页码
    var page: Int = 1
    
    /// 每页记录数
    var limit: Int = 10
    
    /// 浏览记录列表
    var list: [BrowseHistoryModel] = []
}

/// 浏览历史模型
struct BrowseHistoryModel: SmartCodable {
    /// 浏览记录ID
    var log_id: String = ""
    
    /// 浏览记录时间
    var log_created_at: String = ""
    
    /// 帖子ID
    var id: String = ""
    
    /// 帖子发布者用户ID
    var user_id: String = ""
    
    /// 帖子标题
    var title: String = ""
    
    /// 帖子内容描述
    var description: String = ""
    
    /// 图片地址数组
    var img_urls: [String] = []
    
    /// 第一张图片缩略图
    var img: String = ""
    
    /// 金额/预算等
    var price: String?
    
    /// 地理位置
    var location: String = ""
    
    /// 联系方式
    var contact_info: String = ""
    
    /// 帖子状态
    var status: String = ""
    
    /// 创建时间
    var created_at: String = ""
    
    /// 更新时间
    var updated_at: String = ""
    
    /// 可见性标识
    var visibility: String = ""
    
    /// 是否已删除
    var is_deleted: String = ""
    
    /// 浏览次数
    var views: String = ""
    
    /// 数量/人数等
    var quantity: String = ""
    
    /// 原始价格
    var origin_price: String?
    
    /// 物流方式等字段
    var postage_type: String?
    
    /// 帖子类型编号
    var type: Int = 0
    var user: RecommendUserModel?
    var is_liked = false
    
    /// 转换为RecommendModel
    func toRecommendModel() -> RecommendModel {
        var model = RecommendModel()
        model.id = id
        model.userId = user_id
        model.title = title
        model.description = description
        model.img_urls = img_urls
        model.img = img
        model.price = price ?? ""
        model.location = location
        model.contactInfo = contact_info
        model.status = status
        model.created_at = created_at
        model.visibility = visibility
        model.isDeleted = is_deleted
        model.views = views
        model.quantity = quantity
        model.type = type
        model.user = user
        model.is_liked = is_liked
        return model
    }
}
